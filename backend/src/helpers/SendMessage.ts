import Whatsapp from "../models/Whatsapp";
import GetWhatsappWbot from "./GetWhatsappWbot";
import fs from "fs";
import formatBody from "./Mustache";

import { getMessageOptions } from "../services/WbotServices/SendWhatsAppMedia";
import cacheLayer from "../libs/cache";

// Función auxiliar para regenerar sesiones de grupo
const regenerateGroupSession = async (wbot: any, groupJid: string): Promise<void> => {
  try {
    console.log(`🔄 Regenerando sesión para grupo: ${groupJid}`);

    // Limpiar cache de sesiones para este grupo específico
    const groupId = groupJid.replace('@g.us', '');
    await cacheLayer.delFromPattern(`sessions:*:${groupId}*`);
    console.log(`🧹 Cache de sesiones limpiado para grupo: ${groupId}`);

    // Obtener metadatos del grupo (esto fuerza la regeneración de sesiones)
    const groupMetadata = await wbot.groupMetadata(groupJid);
    console.log(`📋 Grupo encontrado: ${groupMetadata.subject} con ${groupMetadata.participants.length} participantes`);

    // Forzar actualización de participantes (esto puede ayudar a regenerar sesiones)
    const participants = groupMetadata.participants.map((p: any) => p.id);
    console.log(`👥 Participantes del grupo: ${participants.length}`);

    // Intentar enviar un mensaje de presencia para establecer sesiones
    try {
      await wbot.sendPresenceUpdate('available', groupJid);
      console.log(`📡 Presencia actualizada para grupo`);
    } catch (presenceErr: any) {
      console.log(`⚠️ No se pudo actualizar presencia:`, presenceErr.message);
    }

    // Esperar un momento para que se procesen las sesiones
    await new Promise(resolve => setTimeout(resolve, 3000));

    console.log(`✅ Sesión regenerada para grupo: ${groupMetadata.subject}`);
  } catch (error: any) {
    console.log(`❌ Error regenerando sesión de grupo:`, error.message);
    throw error;
  }
};

export type MessageData = {
  number: number | string;
  body: string;
  mediaPath?: string;
  companyId?: number;
  mediaName?: string;
};

export const SendMessage = async (
  whatsapp: Whatsapp,
  messageData: MessageData,
  isGroup: boolean = false

): Promise<any> => {
  const wbot = await GetWhatsappWbot(whatsapp);
  const chatId = `${messageData.number}@${!!isGroup ? 'g.us' : 's.whatsapp.net'}`;
  const companyId = messageData?.companyId ? messageData.companyId.toString(): null;

  try {

    let message;

    if (messageData.mediaPath) {
      const options = await getMessageOptions(
        messageData.mediaName,
        messageData.mediaPath,
        companyId,
        messageData.body,
      );
      if (options) {
        const body = fs.readFileSync(messageData.mediaPath);
        message = await wbot.sendMessage(chatId, {
          ...options
        });
      }
    } else {
      const body = formatBody(`${messageData.body}`);
      message = await wbot.sendMessage(chatId, { text: body });
    }

    return message;
  } catch (err: any) {
    console.log(`❌ Error en SendMessage para ${chatId}:`, err.message);

    // Manejo específico para errores de sesión en grupos
    if (err.message && err.message.includes("SessionError") && isGroup) {
      console.log(`🔧 Detectado SessionError en grupo. Intentando regenerar sesión para: ${chatId}`);

      try {
        console.log(`🔧 Primer intento de regeneración de sesión...`);

        // Regenerar sesión del grupo
        await regenerateGroupSession(wbot, chatId);

        // Esperar un momento adicional y reintentar
        await new Promise(resolve => setTimeout(resolve, 1000));

        let retryMessage;
        if (messageData.mediaPath) {
          const options = await getMessageOptions(
            messageData.mediaName,
            messageData.mediaPath,
            companyId,
            messageData.body,
          );
          if (options) {
            retryMessage = await wbot.sendMessage(chatId, {
              ...options
            });
          }
        } else {
          const body = formatBody(`${messageData.body}`);
          retryMessage = await wbot.sendMessage(chatId, { text: body });
        }

        console.log(`✅ Mensaje enviado exitosamente después del reintento en SendMessage`);
        return retryMessage;

      } catch (retryErr: any) {
        console.log(`❌ Primer reintento falló. Intentando método alternativo...`);

        try {
          // Método alternativo: esperar más tiempo y reintentar sin regeneración
          await new Promise(resolve => setTimeout(resolve, 5000));

          let finalRetryMessage;
          if (messageData.mediaPath) {
            const options = await getMessageOptions(
              messageData.mediaName,
              messageData.mediaPath,
              companyId,
              messageData.body,
            );
            if (options) {
              finalRetryMessage = await wbot.sendMessage(chatId, {
                ...options
              });
            }
          } else {
            const body = formatBody(`${messageData.body}`);
            finalRetryMessage = await wbot.sendMessage(chatId, { text: body });
          }

          console.log(`✅ Mensaje enviado exitosamente en segundo reintento`);
          return finalRetryMessage;

        } catch (finalErr: any) {
          console.log(`❌ Todos los reintentos fallaron:`, finalErr.message);
          throw new Error(`SessionError persistente en grupo: ${finalErr.message}`);
        }
      }
    }

    throw new Error(err);
  }
};
