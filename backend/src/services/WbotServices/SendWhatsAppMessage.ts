import { WAMessage, delay } from "@whiskeysockets/baileys";
import * as Sentry from "@sentry/node";
import AppError from "../../errors/AppError";
import GetTicketWbot from "../../helpers/GetTicketWbot";
import Message from "../../models/Message";
import Ticket from "../../models/Ticket";
import Contact from "../../models/Contact";
import { isNil } from "lodash";

import formatBody from "../../helpers/Mustache";

interface Request {
  body: string;
  ticket: Ticket;
  quotedMsg?: Message;
  msdelay?: number;
  vCard?: Contact;
  isForwarded?: boolean;
}

const SendWhatsAppMessage = async ({
  body,
  ticket,
  quotedMsg,
  msdelay,
  vCard,
  isForwarded = false
}: Request): Promise<WAMessage> => {
  let options = {};
  const wbot = await GetTicketWbot(ticket);
  const contactNumber = await Contact.findByPk(ticket.contactId)

  let number: string;

  console.log(`📞 SendWhatsAppMessage - Contact: ${contactNumber.name}`);
  console.log(`📞 SendWhatsAppMessage - Contact Number: ${contactNumber.number}`);
  console.log(`📞 SendWhatsAppMessage - Contact remoteJid: ${contactNumber.remoteJid}`);
  console.log(`📞 SendWhatsAppMessage - Ticket isGroup: ${ticket.isGroup}`);

  if (contactNumber.remoteJid && contactNumber.remoteJid !== "" && contactNumber.remoteJid.includes("@")) {
    number = contactNumber.remoteJid;
    console.log(`📞 SendWhatsAppMessage - Usando remoteJid: ${number}`);
  } else {
    number = `${contactNumber.number}@${ticket.isGroup ? "g.us" : "s.whatsapp.net"}`;
    console.log(`📞 SendWhatsAppMessage - Construyendo número: ${number}`);
  }

  console.log(`📞 SendWhatsAppMessage - Número final: ${number}`);

  if (quotedMsg) {
    const chatMessages = await Message.findOne({
      where: {
        id: quotedMsg.id
      }
    });

    if (chatMessages) {
      const msgFound = JSON.parse(chatMessages.dataJson);


      if (msgFound.message.extendedTextMessage !== undefined) {
        options = {
          quoted: {
            key: msgFound.key,
            message: {
              extendedTextMessage: msgFound.message.extendedTextMessage,
            }
          },
        };
      } else {
        options = {
          quoted: {
            key: msgFound.key,
            message: {
              conversation: msgFound.message.conversation,
            }
          },
        };
      }
    }
  }

  if (!isNil(vCard)) {
    const numberContact = vCard.number;
    const firstName = vCard.name.split(' ')[0];
    const lastName = String(vCard.name).replace(vCard.name.split(' ')[0], '')

    const vcard = `BEGIN:VCARD\n`
      + `VERSION:3.0\n`
      + `N:${lastName};${firstName};;;\n`
      + `FN:${vCard.name}\n`
      + `TEL;type=CELL;waid=${numberContact}:+${numberContact}\n`
      + `END:VCARD`;

    try {
      await delay(msdelay)
      const sentMessage = await wbot.sendMessage(
        number,
        {
          contacts: {
            displayName: `${vCard.name}`,
            contacts: [{ vcard }]
          }
        }
      );
      await ticket.update({ lastMessage: formatBody(vcard, ticket), imported: null });
      return sentMessage;
    } catch (err) {
      Sentry.captureException(err);
      console.log(err);
      throw new AppError("ERR_SENDING_WAPP_MSG");
    }
  };
  try {
    await delay(msdelay)
    const sentMessage = await wbot.sendMessage(
      number,
      {
        text: formatBody(body, ticket),
        contextInfo: { forwardingScore: isForwarded ? 2 : 0, isForwarded: isForwarded ? true : false }
      },
      {
        ...options
      }
    );
    await ticket.update({ lastMessage: formatBody(body, ticket), imported: null });
    return sentMessage;
  } catch (err) {
    console.log(`erro ao enviar mensagem na company ${ticket.companyId} - `, body,
      ticket,
      quotedMsg,
      msdelay,
      vCard,
      isForwarded)
    Sentry.captureException(err);
    console.log(err);

    // Detectar si es un grupo automáticamente
    const isActuallyGroup = ticket.isGroup ||
                           (contactNumber.remoteJid && contactNumber.remoteJid.includes('@g.us')) ||
                           (contactNumber.number && contactNumber.number.toString().length >= 18);

    console.log(`🔍 Detección de grupo: ticket.isGroup=${ticket.isGroup}, isActuallyGroup=${isActuallyGroup}`);
    console.log(`🔍 Error message: "${err.message}"`);
    console.log(`🔍 Error name: "${err.name}"`);
    console.log(`🔍 Error constructor: "${err.constructor.name}"`);
    console.log(`🔍 Error toString: "${err.toString()}"`);
    console.log(`🔍 Error includes SessionError: ${err.message && err.message.includes("SessionError")}`);
    console.log(`🔍 Error toString includes SessionError: ${err.toString().includes("SessionError")}`);

    // Manejo específico para errores de sesión (grupos y individuales)
    const isSessionError = (err.message && err.message.includes("SessionError")) ||
                          (err.toString().includes("SessionError")) ||
                          (err.name === "SessionError") ||
                          (err.constructor.name === "SessionError");

    if (isSessionError) {
      console.log(`🔧 Detectado SessionError. Tipo: ${isActuallyGroup ? 'GRUPO' : 'INDIVIDUAL'}. Regenerando sesión para: ${number}`);

      try {
        if (isActuallyGroup) {
          // Para grupos: obtener metadatos para regenerar sesiones
          console.log(`📋 Obteniendo metadatos del grupo...`);
          const groupMetadata = await wbot.groupMetadata(number);
          console.log(`📋 Metadatos del grupo obtenidos: ${groupMetadata.subject}`);
        } else {
          // Para individuales: intentar obtener info del contacto
          console.log(`📋 Regenerando sesión para contacto individual...`);
        }

        // Esperar un momento y reintentar
        console.log(`⏳ Esperando 3 segundos antes del reintento...`);
        await delay(3000);
        const retryMessage = await wbot.sendMessage(
          number,
          {
            text: formatBody(body, ticket),
            contextInfo: { forwardingScore: isForwarded ? 2 : 0, isForwarded: isForwarded ? true : false }
          },
          {
            ...options
          }
        );

        await ticket.update({ lastMessage: formatBody(body, ticket), imported: null });
        console.log(`✅ Mensaje enviado exitosamente después del reintento`);
        return retryMessage;

      } catch (retryErr) {
        console.log(`❌ Primer reintento falló:`, retryErr.message);

        // SEGUNDO INTENTO: Estrategia alternativa
        try {
          console.log(`🔧 Segundo intento: Estrategia alternativa...`);

          if (isActuallyGroup) {
            // Para grupos: intentar enviar mensaje sin contexto
            console.log(`📋 Intentando envío simplificado para grupo...`);
          }

          // Esperar más tiempo
          console.log(`⏳ Esperando 5 segundos adicionales...`);
          await delay(5000);

          // Reintento con mensaje simplificado
          const secondRetryMessage = await wbot.sendMessage(
            number,
            {
              text: formatBody(body, ticket)
              // Sin contextInfo para evitar problemas de cifrado
            }
          );

          await ticket.update({ lastMessage: formatBody(body, ticket), imported: null });
          console.log(`✅ Mensaje enviado exitosamente en segundo intento`);
          return secondRetryMessage;

        } catch (finalErr) {
          console.log(`❌ Segundo reintento también falló:`, finalErr.message);

          // TERCER INTENTO: Marcar como enviado pero con mensaje de sistema
          console.log(`🔧 Tercer intento: Marcando como enviado con mensaje informativo...`);

          await ticket.update({
            lastMessage: `[SISTEMA] ⚠️ Error de cifrado persistente en ${isActuallyGroup ? 'grupo' : 'contacto'} "${contactNumber.name}". El mensaje no pudo ser entregado debido a problemas de sesión de cifrado. Recomendaciones: 1) Reiniciar la sesión WhatsApp, 2) Verificar que el ${isActuallyGroup ? 'grupo' : 'contacto'} esté activo.`,
            imported: null
          });

          console.log(`📝 Ticket actualizado con mensaje informativo`);
        }
      }
    }

    throw new AppError("ERR_SENDING_WAPP_MSG");
  }
};

export default SendWhatsAppMessage;
