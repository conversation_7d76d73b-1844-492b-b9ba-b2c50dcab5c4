import { WAMessage, delay } from "@whiskeysockets/baileys";
import * as Sentry from "@sentry/node";
import AppError from "../../errors/AppError";
import GetTicketWbot from "../../helpers/GetTicketWbot";
import Message from "../../models/Message";
import Ticket from "../../models/Ticket";
import Contact from "../../models/Contact";
import { isNil } from "lodash";

import formatBody from "../../helpers/Mustache";

interface Request {
  body: string;
  ticket: Ticket;
  quotedMsg?: Message;
  msdelay?: number;
  vCard?: Contact;
  isForwarded?: boolean;
}

const SendWhatsAppMessage = async ({
  body,
  ticket,
  quotedMsg,
  msdelay,
  vCard,
  isForwarded = false
}: Request): Promise<WAMessage> => {
  let options = {};
  const wbot = await GetTicketWbot(ticket);
  const contactNumber = await Contact.findByPk(ticket.contactId)

  let number: string;

  console.log(`📞 SendWhatsAppMessage - Contact: ${contactNumber.name}`);
  console.log(`📞 SendWhatsAppMessage - Contact Number: ${contactNumber.number}`);
  console.log(`📞 SendWhatsAppMessage - Contact remoteJid: ${contactNumber.remoteJid}`);
  console.log(`📞 SendWhatsAppMessage - Ticket isGroup: ${ticket.isGroup}`);

  if (contactNumber.remoteJid && contactNumber.remoteJid !== "" && contactNumber.remoteJid.includes("@")) {
    number = contactNumber.remoteJid;
    console.log(`📞 SendWhatsAppMessage - Usando remoteJid: ${number}`);
  } else {
    number = `${contactNumber.number}@${ticket.isGroup ? "g.us" : "s.whatsapp.net"}`;
    console.log(`📞 SendWhatsAppMessage - Construyendo número: ${number}`);
  }

  console.log(`📞 SendWhatsAppMessage - Número final: ${number}`);

  if (quotedMsg) {
    const chatMessages = await Message.findOne({
      where: {
        id: quotedMsg.id
      }
    });

    if (chatMessages) {
      const msgFound = JSON.parse(chatMessages.dataJson);


      if (msgFound.message.extendedTextMessage !== undefined) {
        options = {
          quoted: {
            key: msgFound.key,
            message: {
              extendedTextMessage: msgFound.message.extendedTextMessage,
            }
          },
        };
      } else {
        options = {
          quoted: {
            key: msgFound.key,
            message: {
              conversation: msgFound.message.conversation,
            }
          },
        };
      }
    }
  }

  if (!isNil(vCard)) {
    const numberContact = vCard.number;
    const firstName = vCard.name.split(' ')[0];
    const lastName = String(vCard.name).replace(vCard.name.split(' ')[0], '')

    const vcard = `BEGIN:VCARD\n`
      + `VERSION:3.0\n`
      + `N:${lastName};${firstName};;;\n`
      + `FN:${vCard.name}\n`
      + `TEL;type=CELL;waid=${numberContact}:+${numberContact}\n`
      + `END:VCARD`;

    try {
      await delay(msdelay)
      const sentMessage = await wbot.sendMessage(
        number,
        {
          contacts: {
            displayName: `${vCard.name}`,
            contacts: [{ vcard }]
          }
        }
      );
      await ticket.update({ lastMessage: formatBody(vcard, ticket), imported: null });
      return sentMessage;
    } catch (err) {
      Sentry.captureException(err);
      console.log(err);
      throw new AppError("ERR_SENDING_WAPP_MSG");
    }
  };
  try {
    await delay(msdelay)
    const sentMessage = await wbot.sendMessage(
      number,
      {
        text: formatBody(body, ticket),
        contextInfo: { forwardingScore: isForwarded ? 2 : 0, isForwarded: isForwarded ? true : false }
      },
      {
        ...options
      }
    );
    await ticket.update({ lastMessage: formatBody(body, ticket), imported: null });
    return sentMessage;
  } catch (err) {
    console.log(`erro ao enviar mensagem na company ${ticket.companyId} - `, body,
      ticket,
      quotedMsg,
      msdelay,
      vCard,
      isForwarded)
    Sentry.captureException(err);
    console.log(err);

    // Manejo específico para errores de sesión en grupos
    if (err.message && err.message.includes("SessionError") && ticket.isGroup) {
      console.log(`🔧 Detectado SessionError en grupo. Intentando regenerar sesión para: ${number}`);

      try {
        // Intentar obtener metadatos del grupo para regenerar la sesión
        const groupMetadata = await wbot.groupMetadata(number);
        console.log(`📋 Metadatos del grupo obtenidos: ${groupMetadata.subject}`);

        // Esperar un momento y reintentar
        await delay(2000);
        const retryMessage = await wbot.sendMessage(
          number,
          {
            text: formatBody(body, ticket),
            contextInfo: { forwardingScore: isForwarded ? 2 : 0, isForwarded: isForwarded ? true : false }
          },
          {
            ...options
          }
        );

        await ticket.update({ lastMessage: formatBody(body, ticket), imported: null });
        console.log(`✅ Mensaje enviado exitosamente después del reintento`);
        return retryMessage;

      } catch (retryErr) {
        console.log(`❌ Reintento falló:`, retryErr.message);
        // Actualizar el ticket con un mensaje informativo
        await ticket.update({
          lastMessage: `[SISTEMA] ⚠️ Error de cifrado en grupo "${contactNumber.name}". Posibles causas: 1) Verifique que 'Permitir Grupos' esté habilitado en WhatsApp y Usuario. 2) Reinicie la sesión WhatsApp escaneando el código QR nuevamente.`,
          imported: null
        });
      }
    }

    throw new AppError("ERR_SENDING_WAPP_MSG");
  }
};

export default SendWhatsAppMessage;
