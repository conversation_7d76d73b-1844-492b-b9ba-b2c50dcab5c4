// Script alternativo para monitorear logs desde archivos
const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

console.log('🔍 MONITOR DE LOGS DE GRUPOS (Versión Archivo) - WhaConnect');
console.log('==========================================================\n');

// Función para formatear timestamp
function getTimestamp() {
  return new Date().toLocaleString('es-MX', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
}

// Función para colorear logs
function colorLog(message, type = 'info') {
  const colors = {
    info: '\x1b[36m',      // Cyan
    success: '\x1b[32m',   // Green
    error: '\x1b[31m',     // Red
    warning: '\x1b[33m',   // Yellow
    debug: '\x1b[35m',     // Magenta
    reset: '\x1b[0m'       // Reset
  };
  
  const timestamp = `[${getTimestamp()}]`;
  console.log(`${colors[type]}${timestamp} ${message}${colors.reset}`);
}

// Palabras clave para filtrar logs relevantes
const keywords = [
  'SendWhatsAppMessage',
  'SendMessage', 
  'SessionError',
  'grupo',
  'group',
  '@g.us',
  'isGroup',
  'remoteJid',
  'Detectado SessionError',
  'regenerar sesión',
  'Metadatos del grupo',
  'enviado exitosamente',
  'Reintento falló',
  'Error de cifrado',
  'Contact Number',
  'Número final',
  'Detección de grupo'
];

// Función para verificar si una línea contiene palabras clave
function isRelevantLog(line) {
  const lowerLine = line.toLowerCase();
  return keywords.some(keyword => lowerLine.includes(keyword.toLowerCase()));
}

// Función para determinar el tipo de log
function getLogType(line) {
  const lowerLine = line.toLowerCase();
  
  if (lowerLine.includes('✅') || lowerLine.includes('enviado exitosamente')) {
    return 'success';
  } else if (lowerLine.includes('❌') || lowerLine.includes('error') || lowerLine.includes('falló')) {
    return 'error';
  } else if (lowerLine.includes('🔧') || lowerLine.includes('regenerar') || lowerLine.includes('reintento')) {
    return 'warning';
  } else if (lowerLine.includes('📞') || lowerLine.includes('🔍') || lowerLine.includes('📋')) {
    return 'debug';
  } else {
    return 'info';
  }
}

// Buscar archivos de log de PM2
function findPM2LogFiles() {
  const possiblePaths = [
    path.join(process.env.HOME, '.pm2/logs'),
    '/root/.pm2/logs',
    path.join(__dirname, '../.pm2/logs'),
    '/var/log/pm2'
  ];

  for (const logPath of possiblePaths) {
    try {
      if (fs.existsSync(logPath)) {
        const files = fs.readdirSync(logPath);
        const backendLogs = files.filter(file => 
          file.includes('whaconnect-backend') && file.includes('out')
        );
        
        if (backendLogs.length > 0) {
          const fullPath = path.join(logPath, backendLogs[0]);
          colorLog(`Encontrado archivo de log: ${fullPath}`, 'success');
          return fullPath;
        }
      }
    } catch (err) {
      // Continuar buscando
    }
  }
  
  return null;
}

// Función principal
function startMonitoring() {
  const logFile = findPM2LogFiles();
  
  if (!logFile) {
    colorLog('No se encontraron archivos de log de PM2', 'error');
    console.log('\n💡 ALTERNATIVAS:');
    console.log('1. Usar: node monitor_group_logs.js (versión con pm2 logs)');
    console.log('2. Verificar la ubicación de logs PM2 con: pm2 show whaconnect-backend');
    console.log('3. Usar tail directamente: tail -f ~/.pm2/logs/whaconnect-backend-out.log');
    return;
  }

  colorLog('Iniciando monitoreo de logs...', 'info');
  console.log('Presiona Ctrl+C para salir\n');

  // Usar tail para seguir el archivo
  const tail = spawn('tail', ['-f', logFile], {
    stdio: ['pipe', 'pipe', 'pipe']
  });

  tail.stdout.on('data', (data) => {
    const lines = data.toString().split('\n');
    
    lines.forEach(line => {
      if (line.trim() && isRelevantLog(line)) {
        const logType = getLogType(line);
        
        // Limpiar la línea de caracteres de control
        const cleanLine = line.replace(/^\d+\|whaconnect-backend\s*\|\s*/, '');
        
        colorLog(cleanLine, logType);
      }
    });
  });

  tail.stderr.on('data', (data) => {
    const errorMsg = data.toString();
    if (errorMsg.trim()) {
      colorLog(`ERROR TAIL: ${errorMsg}`, 'error');
    }
  });

  tail.on('close', (code) => {
    colorLog(`Monitor terminado con código: ${code}`, 'warning');
  });

  tail.on('error', (err) => {
    colorLog(`Error con tail: ${err.message}`, 'error');
  });

  // Manejar Ctrl+C
  process.on('SIGINT', () => {
    colorLog('\n👋 Deteniendo monitor...', 'warning');
    tail.kill();
    process.exit(0);
  });
}

// Mostrar instrucciones y iniciar
console.log('📋 INSTRUCCIONES:');
console.log('- Este monitor lee directamente los archivos de log de PM2');
console.log('- Filtra solo logs relevantes para grupos');
console.log('- Envía un mensaje a un grupo para ver los logs en tiempo real');
console.log('- Los logs se colorean según su tipo\n');

startMonitoring();

// Mostrar ayuda adicional después de un momento
setTimeout(() => {
  console.log('\n🎯 PARA PROBAR:');
  console.log('1. Envía un mensaje a cualquier grupo desde WhaConnect');
  console.log('2. Observa los logs filtrados aquí');
  console.log('3. Busca especialmente logs con 📞, 🔧, ✅ o ❌\n');
}, 2000);
