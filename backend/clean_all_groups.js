// Script para limpiar TODOS los grupos de TODAS las conexiones WhatsApp
const { Sequelize } = require('sequelize');

// Configuración de la base de datos
const sequelize = new Sequelize(process.env.DB_NAME || 'whaconnect', process.env.DB_USER || 'whaconnect', process.env.DB_PASS || 'whaconnect', {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  dialect: 'postgres',
  logging: false
});

async function cleanAllGroups() {
  try {
    console.log('🧹 LIMPIEZA MASIVA DE TODOS LOS GRUPOS\n');
    console.log('⚠️  ADVERTENCIA: Esta operación eliminará TODOS los grupos de TODAS las conexiones');
    console.log('   - Se perderá el historial de mensajes de grupos');
    console.log('   - Los grupos se recrearán automáticamente cuando lleguen nuevos mensajes');
    console.log('   - Esto solucionará problemas de cifrado en todos los grupos\n');

    // 1. Identificar todos los contactos de grupos
    console.log('🔍 Identificando contactos de grupos...');
    const [groupContacts] = await sequelize.query(`
      SELECT 
        c.id,
        c.name,
        c.number,
        c."remoteJid",
        c."companyId",
        COUNT(t.id) as ticket_count,
        COUNT(m.id) as message_count
      FROM "Contacts" c
      LEFT JOIN "Tickets" t ON c.id = t."contactId"
      LEFT JOIN "Messages" m ON t.id = m."ticketId"
      WHERE c."remoteJid" LIKE '%@g.us'
         OR c.number ~ '^[0-9]{15,}$'
      GROUP BY c.id, c.name, c.number, c."remoteJid", c."companyId"
      ORDER BY c."companyId", c.name
    `);

    if (groupContacts.length === 0) {
      console.log('✅ No se encontraron grupos para limpiar');
      return;
    }

    console.log(`📊 Grupos encontrados: ${groupContacts.length}\n`);

    // 2. Mostrar resumen por compañía
    const companySummary = {};
    let totalTickets = 0;
    let totalMessages = 0;

    groupContacts.forEach(contact => {
      const companyId = contact.companyId;
      if (!companySummary[companyId]) {
        companySummary[companyId] = {
          groups: 0,
          tickets: 0,
          messages: 0
        };
      }
      companySummary[companyId].groups++;
      companySummary[companyId].tickets += parseInt(contact.ticket_count);
      companySummary[companyId].messages += parseInt(contact.message_count);
      
      totalTickets += parseInt(contact.ticket_count);
      totalMessages += parseInt(contact.message_count);
    });

    console.log('📋 RESUMEN POR COMPAÑÍA:');
    for (const [companyId, summary] of Object.entries(companySummary)) {
      console.log(`   Company ${companyId}: ${summary.groups} grupos, ${summary.tickets} tickets, ${summary.messages} mensajes`);
    }
    console.log(`\n📊 TOTALES: ${groupContacts.length} grupos, ${totalTickets} tickets, ${totalMessages} mensajes\n`);

    // 3. Mostrar algunos ejemplos
    console.log('📝 EJEMPLOS DE GRUPOS A ELIMINAR:');
    groupContacts.slice(0, 5).forEach((contact, index) => {
      console.log(`   ${index + 1}. ${contact.name} (${contact.number}) - Company ${contact.companyId}`);
    });
    if (groupContacts.length > 5) {
      console.log(`   ... y ${groupContacts.length - 5} grupos más`);
    }

    console.log('\n🗑️ INICIANDO LIMPIEZA MASIVA...\n');

    // 4. Eliminar en lotes para mejor rendimiento
    const batchSize = 10;
    let processedGroups = 0;

    for (let i = 0; i < groupContacts.length; i += batchSize) {
      const batch = groupContacts.slice(i, i + batchSize);
      const contactIds = batch.map(c => c.id).join(',');

      console.log(`📦 Procesando lote ${Math.floor(i/batchSize) + 1}/${Math.ceil(groupContacts.length/batchSize)} (${batch.length} grupos)...`);

      // Eliminar mensajes del lote
      await sequelize.query(`
        DELETE FROM "Messages" 
        WHERE "ticketId" IN (
          SELECT id FROM "Tickets" WHERE "contactId" IN (${contactIds})
        )
      `);

      // Eliminar tickets del lote
      await sequelize.query(`
        DELETE FROM "Tickets" 
        WHERE "contactId" IN (${contactIds})
      `);

      // Eliminar contactos del lote
      await sequelize.query(`
        DELETE FROM "Contacts" 
        WHERE id IN (${contactIds})
      `);

      processedGroups += batch.length;
      console.log(`   ✅ Lote completado (${processedGroups}/${groupContacts.length} grupos procesados)`);
    }

    console.log('\n🧹 Limpiando datos adicionales...');

    // 5. Limpiar tickets huérfanos de grupos (por si acaso)
    await sequelize.query(`
      DELETE FROM "Tickets" 
      WHERE "isGroup" = true 
      AND "contactId" NOT IN (SELECT id FROM "Contacts")
    `);

    // 6. Limpiar mensajes huérfanos
    await sequelize.query(`
      DELETE FROM "Messages" 
      WHERE "ticketId" NOT IN (SELECT id FROM "Tickets")
    `);

    console.log('\n✅ LIMPIEZA MASIVA COMPLETADA EXITOSAMENTE!\n');

    console.log('🎯 RESULTADOS:');
    console.log(`- ✅ ${groupContacts.length} contactos de grupos eliminados`);
    console.log(`- ✅ ${totalTickets} tickets eliminados`);
    console.log(`- ✅ ${totalMessages} mensajes eliminados`);
    console.log('- ✅ Datos huérfanos limpiados');
    console.log('- ✅ Cache automáticamente invalidado');

    console.log('\n📱 PRÓXIMOS PASOS:');
    console.log('1. Todos los grupos se recrearán automáticamente cuando:');
    console.log('   - Llegue un mensaje desde cualquier grupo');
    console.log('   - Se envíe un mensaje a cualquier grupo');
    console.log('');
    console.log('2. Beneficios obtenidos:');
    console.log('   - ✅ Sesiones de cifrado completamente limpias');
    console.log('   - ✅ Sin errores "SessionError: No sessions"');
    console.log('   - ✅ Todos los grupos funcionarán correctamente');
    console.log('   - ✅ Configuración groupAsTicket aplicada correctamente');

    console.log('\n⚠️ IMPORTANTE:');
    console.log('- Se perdió el historial de mensajes de TODOS los grupos');
    console.log('- Pero se solucionaron TODOS los problemas de cifrado');
    console.log('- Los grupos funcionarán perfectamente a partir de ahora');
    console.log('- La configuración de WhatsApp (allowGroup, groupAsTicket) se mantiene intacta');

  } catch (error) {
    console.error('❌ Error durante la limpieza masiva:', error.message);
    console.error('Stack:', error.stack);
  } finally {
    await sequelize.close();
  }
}

cleanAllGroups();
