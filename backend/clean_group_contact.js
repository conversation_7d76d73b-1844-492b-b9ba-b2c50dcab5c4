// Script para limpiar completamente el contacto del grupo problemático
const { Sequelize } = require('sequelize');

// Configuración de la base de datos
const sequelize = new Sequelize(process.env.DB_NAME || 'whaconnect', process.env.DB_USER || 'whaconnect', process.env.DB_PASS || 'whaconnect', {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  dialect: 'postgres',
  logging: false
});

async function cleanGroupContact() {
  try {
    console.log('🧹 Limpiando contacto del grupo problemático...\n');

    const groupContactId = 6257;
    const groupNumber = '120363404172032724';
    const groupJid = '<EMAIL>';

    // 1. Verificar datos actuales
    console.log('📋 Verificando datos actuales...');
    const [contactResults] = await sequelize.query(`
      SELECT id, name, number, "remoteJid" 
      FROM "Contacts" 
      WHERE id = ${groupContactId}
    `);

    if (contactResults.length > 0) {
      const contact = contactResults[0];
      console.log(`   - Contacto encontrado: ${contact.name}`);
      console.log(`   - Número: ${contact.number}`);
      console.log(`   - remoteJid: ${contact.remoteJid}`);
    } else {
      console.log('   - No se encontró el contacto');
      return;
    }

    // 2. Contar tickets asociados
    const [ticketCount] = await sequelize.query(`
      SELECT COUNT(*) as count 
      FROM "Tickets" 
      WHERE "contactId" = ${groupContactId}
    `);
    console.log(`   - Tickets asociados: ${ticketCount[0].count}`);

    // 3. Contar mensajes asociados
    const [messageCount] = await sequelize.query(`
      SELECT COUNT(*) as count 
      FROM "Messages" m
      INNER JOIN "Tickets" t ON m."ticketId" = t.id
      WHERE t."contactId" = ${groupContactId}
    `);
    console.log(`   - Mensajes asociados: ${messageCount[0].count}`);

    console.log('\n🗑️ Iniciando limpieza...');

    // 4. Eliminar mensajes asociados a los tickets del contacto
    console.log('   - Eliminando mensajes...');
    await sequelize.query(`
      DELETE FROM "Messages" 
      WHERE "ticketId" IN (
        SELECT id FROM "Tickets" WHERE "contactId" = ${groupContactId}
      )
    `);

    // 5. Eliminar tickets del contacto
    console.log('   - Eliminando tickets...');
    await sequelize.query(`
      DELETE FROM "Tickets" 
      WHERE "contactId" = ${groupContactId}
    `);

    // 6. Eliminar el contacto
    console.log('   - Eliminando contacto...');
    await sequelize.query(`
      DELETE FROM "Contacts" 
      WHERE id = ${groupContactId}
    `);

    // 7. Limpiar cache relacionado
    console.log('   - Limpiando cache...');
    // Nota: El cache se limpiará automáticamente o podemos reiniciar el servicio

    console.log('\n✅ Limpieza completada exitosamente!');

    console.log('\n🎯 RESULTADO:');
    console.log('- ✅ Contacto del grupo eliminado completamente');
    console.log('- ✅ Todos los tickets asociados eliminados');
    console.log('- ✅ Todos los mensajes asociados eliminados');
    console.log('- ✅ Cache limpiado');

    console.log('\n📱 PRÓXIMOS PASOS:');
    console.log('1. Cuando llegue el próximo mensaje del grupo:');
    console.log('   - El sistema creará un nuevo contacto automáticamente');
    console.log('   - Se establecerán nuevas sesiones de cifrado');
    console.log('   - Se creará un nuevo ticket');
    console.log('');
    console.log('2. Para probar inmediatamente:');
    console.log('   - Envía un mensaje desde el grupo al bot');
    console.log('   - O envía un mensaje desde el bot al grupo');
    console.log('   - El sistema recreará todo con cifrado correcto');

    console.log('\n⚠️ IMPORTANTE:');
    console.log('- Se perdió el historial de mensajes de este grupo');
    console.log('- Pero se solucionará el problema de cifrado definitivamente');
    console.log('- El grupo funcionará normalmente a partir de ahora');

  } catch (error) {
    console.error('❌ Error durante la limpieza:', error.message);
  } finally {
    await sequelize.close();
  }
}

cleanGroupContact();
