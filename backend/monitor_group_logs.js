// Script para monitorear logs de grupos en tiempo real
const { spawn } = require('child_process');
const fs = require('fs');

console.log('🔍 MONITOR DE LOGS DE GRUPOS - WhaConnect');
console.log('=========================================\n');
console.log('Monitoreando logs relacionados con envío de mensajes a grupos...');
console.log('Presiona Ctrl+C para salir\n');

// Función para formatear timestamp
function getTimestamp() {
  return new Date().toLocaleString('es-MX', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
}

// Función para colorear logs
function colorLog(message, type = 'info') {
  const colors = {
    info: '\x1b[36m',      // Cyan
    success: '\x1b[32m',   // Green
    error: '\x1b[31m',     // Red
    warning: '\x1b[33m',   // Yellow
    debug: '\x1b[35m',     // Magenta
    reset: '\x1b[0m'       // Reset
  };
  
  const timestamp = `[${getTimestamp()}]`;
  console.log(`${colors[type]}${timestamp} ${message}${colors.reset}`);
}

// Palabras clave para filtrar logs relevantes
const keywords = [
  'SendWhatsAppMessage',
  'SendMessage',
  'SessionError',
  'grupo',
  'group',
  '@g.us',
  'isGroup',
  'remoteJid',
  'Detectado SessionError',
  'regenerar sesión',
  'Metadatos del grupo',
  'enviado exitosamente',
  'Reintento falló',
  'Error de cifrado',
  'Contact Number',
  'Número final',
  'Detección de grupo'
];

// Función para verificar si una línea contiene palabras clave
function isRelevantLog(line) {
  const lowerLine = line.toLowerCase();
  return keywords.some(keyword => lowerLine.includes(keyword.toLowerCase()));
}

// Función para determinar el tipo de log
function getLogType(line) {
  const lowerLine = line.toLowerCase();
  
  if (lowerLine.includes('✅') || lowerLine.includes('enviado exitosamente')) {
    return 'success';
  } else if (lowerLine.includes('❌') || lowerLine.includes('error') || lowerLine.includes('falló')) {
    return 'error';
  } else if (lowerLine.includes('🔧') || lowerLine.includes('regenerar') || lowerLine.includes('reintento')) {
    return 'warning';
  } else if (lowerLine.includes('📞') || lowerLine.includes('🔍') || lowerLine.includes('📋')) {
    return 'debug';
  } else {
    return 'info';
  }
}

// Iniciar monitoreo de logs de PM2
const pm2Logs = spawn('pm2', ['logs', 'whaconnect-backend', '--lines', '0'], {
  stdio: ['pipe', 'pipe', 'pipe']
});

let logBuffer = '';

pm2Logs.stdout.on('data', (data) => {
  logBuffer += data.toString();
  
  // Procesar líneas completas
  const lines = logBuffer.split('\n');
  logBuffer = lines.pop(); // Mantener la línea incompleta en el buffer
  
  lines.forEach(line => {
    if (line.trim() && isRelevantLog(line)) {
      const logType = getLogType(line);
      
      // Limpiar la línea de caracteres de control de PM2
      const cleanLine = line.replace(/^\d+\|whaconnect-backend\s*\|\s*/, '');
      
      colorLog(cleanLine, logType);
    }
  });
});

pm2Logs.stderr.on('data', (data) => {
  const errorMsg = data.toString();
  if (errorMsg.trim()) {
    colorLog(`ERROR PM2: ${errorMsg}`, 'error');
  }
});

pm2Logs.on('close', (code) => {
  colorLog(`Monitor de logs terminado con código: ${code}`, 'warning');
});

pm2Logs.on('error', (err) => {
  colorLog(`Error iniciando monitor: ${err.message}`, 'error');
  console.log('\n💡 ALTERNATIVAS:');
  console.log('1. Verificar que PM2 esté instalado: npm install -g pm2');
  console.log('2. Verificar que el proceso se llame "whaconnect-backend"');
  console.log('3. Usar: pm2 list para ver los procesos disponibles');
  console.log('4. Ejecutar: node monitor_group_logs_file.js (versión alternativa)');
  process.exit(1);
});

// Manejar Ctrl+C
process.on('SIGINT', () => {
  colorLog('\n👋 Deteniendo monitor de logs...', 'warning');
  pm2Logs.kill();
  process.exit(0);
});

// Mostrar instrucciones
setTimeout(() => {
  console.log('\n📋 INSTRUCCIONES:');
  console.log('- Este monitor filtra solo logs relevantes para grupos');
  console.log('- Envía un mensaje a un grupo para ver los logs en tiempo real');
  console.log('- Los logs se colorean según su tipo (éxito, error, debug, etc.)');
  console.log('- Presiona Ctrl+C para salir\n');
}, 1000);
