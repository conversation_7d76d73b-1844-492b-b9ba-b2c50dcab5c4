// Script de debug para verificar configuración de grupos
const { Sequelize } = require('sequelize');

// Configuración de la base de datos (ajustar según tu configuración)
const sequelize = new Sequelize(process.env.DB_NAME || 'whaconnect', process.env.DB_USER || 'postgres', process.env.DB_PASS || '', {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  dialect: 'postgres',
  logging: false
});

async function debugGroupConfig() {
  try {
    console.log('🔍 Verificando configuración de grupos...\n');

    // Verificar WhatsApp ID 33 (del log)
    const [whatsappResults] = await sequelize.query(`
      SELECT id, name, "allowGroup", "groupAsTicket", status 
      FROM "Whatsapps" 
      WHERE id = 33
    `);

    if (whatsappResults.length > 0) {
      const whatsapp = whatsappResults[0];
      console.log('📱 WhatsApp ID 33:');
      console.log(`   - Nombre: ${whatsapp.name}`);
      console.log(`   - allowGroup: ${whatsapp.allowGroup}`);
      console.log(`   - groupAsTicket: ${whatsapp.groupAsTicket}`);
      console.log(`   - Status: ${whatsapp.status}`);
      console.log('');

      if (!whatsapp.allowGroup) {
        console.log('❌ PROBLEMA ENCONTRADO: allowGroup está en FALSE');
        console.log('   Esto bloquea el procesamiento de mensajes de grupos');
        console.log('');
      } else {
        console.log('✅ allowGroup está habilitado');
        console.log('');
      }
    } else {
      console.log('❌ No se encontró WhatsApp con ID 33');
    }

    // Verificar contacto del grupo
    const [contactResults] = await sequelize.query(`
      SELECT id, name, number, "remoteJid" 
      FROM "Contacts" 
      WHERE id = 6257
    `);

    if (contactResults.length > 0) {
      const contact = contactResults[0];
      console.log('👥 Contacto ID 6257 (Grupo):');
      console.log(`   - Nombre: ${contact.name}`);
      console.log(`   - Número: ${contact.number}`);
      console.log(`   - remoteJid: ${contact.remoteJid}`);
      console.log('');

      // Verificar si el formato es correcto
      if (contact.remoteJid && contact.remoteJid.endsWith('@g.us')) {
        console.log('✅ remoteJid tiene formato correcto para grupo');
      } else {
        console.log('❌ remoteJid no tiene formato correcto para grupo');
      }
      console.log('');
    }

    // Verificar ticket
    const [ticketResults] = await sequelize.query(`
      SELECT id, "isGroup", status, "contactId", "whatsappId"
      FROM "Tickets" 
      WHERE id = 8995
    `);

    if (ticketResults.length > 0) {
      const ticket = ticketResults[0];
      console.log('🎫 Ticket ID 8995:');
      console.log(`   - isGroup: ${ticket.isGroup}`);
      console.log(`   - Status: ${ticket.status}`);
      console.log(`   - contactId: ${ticket.contactId}`);
      console.log(`   - whatsappId: ${ticket.whatsappId}`);
      console.log('');
    }

    console.log('🔧 RECOMENDACIONES:');
    if (whatsappResults.length > 0 && !whatsappResults[0].allowGroup) {
      console.log('1. Habilitar allowGroup para WhatsApp ID 33:');
      console.log('   UPDATE "Whatsapps" SET "allowGroup" = true WHERE id = 33;');
      console.log('');
    }
    console.log('2. Verificar que mi modificación en queues.ts esté funcionando');
    console.log('3. Reiniciar el servicio backend después de los cambios');

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await sequelize.close();
  }
}

debugGroupConfig();
